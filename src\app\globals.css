@import "tailwindcss";

:root {
  /* Brand / Primary */
  --primary: #151156; /* warna utama brand, CTA utama, link penting */
  --on-primary: #FFFFFF; /* teks/ikon di atas --primary */

  /* Secondary */
  --secondary: #4D528E; /* sekunder untuk section headers, secondary CTA, cards header */
  --on-secondary: #FFFFFF; /* teks di atas --secondary (jika dipakai sebagai surface gelap) */
  --secondary-muted: #465058; /* versi netral untuk teks sekunder / borders */

  /* Background & Surface - Dark Theme */
  --background: #272C32; /* halaman / large surfaces - dark background */
  --surface: #465058; /* cards / panels - medium dark */
  --on-surface: #FFFFFF; /* teks utama di atas --surface - white for contrast */

  /* Text - Dark Theme */
  --text-primary: #FFFFFF; /* body, paragraf, default - white for dark bg */
  --text-secondary: #F8F9FC; /* caption, meta, helper text - light gray */
  --text-strong: #FFFFFF; /* display headings - white for maximum contrast */

  /* Accent / Highlight - Adjusted for Dark Theme */
  --accent: #A4C6EC; /* highlight lembut, chips, subtle badges, tooltips (jangan untuk body text) */
  --accent-2: #91ABCB; /* state/hover/active pada accent, atau badge warna lain */

  /* Dark UI / Nav / Footer */
  --dark-1: #1C1F37; /* navbar/footer background, big solid accents */
  --dark-2: #272C32; /* alternatif teks di atas surface gelap atau elemen display */
  --black: #000000; /* VERY limited: strokes kecil, ikon mikro; gunakan opacity bila mungkin */

  /* Border / Divider / Shadow - Dark Theme */
  --border: rgba(0, 0, 0, 0.3); /* subtle border menggunakan black dengan opacity */
  --shadow: rgba(0, 0, 0, 0.4); /* shadow lebih kuat untuk dark theme */

  /* Status (opsional) */
  --success: #2E7D32;
  --warning: #F2994A;
  --danger: #E63946;

  /* Legacy mappings for backward compatibility */
  --foreground: var(--text-primary);
  --muted-foreground: var(--text-primary);
  --muted-foreground-light: var(--text-secondary);
  --brand-primary: var(--primary);
  --brand-secondary: var(--secondary);
  --brand-accent: var(--accent);
  --primary-button-bg: var(--primary);
  --primary-button-text: var(--on-primary);
  --primary-button-hover: var(--text-primary);
  --secondary-button-bg: transparent;
  --secondary-button-text: var(--primary);
  --secondary-button-border: var(--primary);
  --secondary-button-hover: var(--background);
  --card-bg: var(--surface);
  --card-border: var(--border);
  --hero-gradient: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
}

@theme inline {
  /* Brand / Primary */
  --color-primary: var(--primary);
  --color-on-primary: var(--on-primary);

  /* Secondary */
  --color-secondary: var(--secondary);
  --color-on-secondary: var(--on-secondary);
  --color-secondary-muted: var(--secondary-muted);

  /* Background & Surface */
  --color-background: var(--background);
  --color-surface: var(--surface);
  --color-on-surface: var(--on-surface);

  /* Text */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-strong: var(--text-strong);

  /* Accent / Highlight */
  --color-accent: var(--accent);
  --color-accent-2: var(--accent-2);

  /* Dark UI / Nav / Footer */
  --color-dark-1: var(--dark-1);
  --color-dark-2: var(--dark-2);
  --color-black: var(--black);

  /* Border / Divider / Shadow */
  --color-border: var(--border);
  --color-shadow: var(--shadow);

  /* Status */
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-danger: var(--danger);

  /* Legacy mappings for backward compatibility */
  --color-foreground: var(--foreground);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted-foreground-light: var(--muted-foreground-light);
  --color-brand-primary: var(--brand-primary);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-accent: var(--brand-accent);
  --color-primary-button-bg: var(--primary-button-bg);
  --color-primary-button-text: var(--primary-button-text);
  --color-primary-button-hover: var(--primary-button-hover);
  --color-secondary-button-bg: var(--secondary-button-bg);
  --color-secondary-button-text: var(--secondary-button-text);
  --color-secondary-button-border: var(--secondary-button-border);
  --color-secondary-button-hover: var(--secondary-button-hover);
  --color-card-bg: var(--card-bg);
  --color-card-border: var(--card-border);
  --color-hero-gradient: var(--hero-gradient);

  /* Fonts */
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

body {
  font-family: var(--font-inter), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background: var(--background);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--surface);
}

/* Chrome, Edge, Safari */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
*::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: 8px;
}
*::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 8px;
  border: 2px solid var(--surface);
}
*::-webkit-scrollbar-thumb:hover {
  background: var(--text-primary);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial;
}

/* Smooth transitions for interactive elements */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for better accessibility */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(164, 198, 236, 0.18); /* using --accent with alpha */
}

/* High contrast mode support - Dark Theme */
@media (prefers-contrast: high) {
  :root {
    --text-secondary: var(--text-primary);
    --secondary-muted: var(--text-primary);
    --border: rgba(255, 255, 255, 0.2); /* lighter borders for high contrast */
    --surface: var(--black); /* more contrast between surface and background */
  }
}

/* Utility classes following color scheme rules */

/* Primary CTA Button */
.btn-primary {
  background: var(--primary);
  color: var(--on-primary);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: var(--text-primary);
  /* or use filter: brightness(0.92); */
}

/* Secondary CTA Button */
.btn-secondary {
  background: var(--secondary);
  color: var(--on-secondary);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Ghost/Outline Button */
.btn-ghost {
  background: transparent;
  border: 1.5px solid var(--primary);
  color: var(--primary);
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-ghost:hover {
  background: var(--primary);
  color: var(--on-primary);
}

/* Card styling */
.card {
  background: var(--surface);
  border: 1px solid var(--border);
  box-shadow: 0 6px 20px var(--shadow);
  border-radius: 12px;
  padding: 24px;
}

/* Badge/Chip styling - Dark Theme */
.badge {
  background: var(--accent);
  color: var(--background); /* dark text on light accent for better contrast */
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-block;
}

/* Small caption text */
.caption {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Navbar - Using darkest color */
.nav {
  background: var(--black);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border);
}

/* Footer - Using darkest color */
.footer {
  background: var(--black);
  border-top: 1px solid var(--border);
}

.footer a {
  color: var(--accent-2);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: var(--accent);
}

/* Input focus state - Dark Theme */
input:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(164, 198, 236, 0.25); /* stronger glow for dark theme */
  border-color: var(--primary);
  background: var(--surface);
  color: var(--text-primary);
}

/* Input styling for dark theme */
input,
textarea,
select {
  background: var(--surface);
  border: 1px solid var(--border);
  color: var(--text-primary);
  border-radius: 6px;
  padding: 8px 12px;
}

input::placeholder,
textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Dark theme specific utilities */
.text-on-dark {
  color: var(--text-primary);
}

.text-secondary-dark {
  color: var(--text-secondary);
}

.bg-surface-dark {
  background: var(--surface);
}

.bg-background-dark {
  background: var(--background);
}

.border-dark {
  border-color: var(--border);
}
